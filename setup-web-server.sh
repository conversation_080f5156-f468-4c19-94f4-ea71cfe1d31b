#!/bin/bash

# 智能调度系统Web服务器配置脚本
# 适用于现有PHP环境的快速部署

set -e

# 数据库连接测试函数
test_database_connection() {
    local host=$1
    local port=$2
    local database=$3
    local username=$4
    local password=$5

    php -r "
    try {
        \$pdo = new PDO('mysql:host=$host;port=$port;dbname=$database', '$username', '$password');
        \$pdo->setAttribute(PDO::ATTR_ERRMODE, PDO::ERRMODE_EXCEPTION);
        echo 'SUCCESS: 数据库连接成功';
        exit(0);
    } catch (PDOException \$e) {
        echo 'ERROR: ' . \$e->getMessage();
        exit(1);
    }
    " 2>/dev/null
}

# 颜色定义
RED='\033[0;31m'
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
BLUE='\033[0;34m'
CYAN='\033[0;36m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}[INFO]${NC} $1"
}

log_success() {
    echo -e "${GREEN}[SUCCESS]${NC} $1"
}

log_warning() {
    echo -e "${YELLOW}[WARNING]${NC} $1"
}

log_error() {
    echo -e "${RED}[ERROR]${NC} $1"
}

log_step() {
    echo -e "${CYAN}[STEP]${NC} $1"
}

echo "========================================"
echo "    智能调度系统Web服务器配置向导"
echo "========================================"
echo ""

# 检查项目根目录
check_project_root() {
    if [ ! -f "public/index.php" ]; then
        log_error "请在项目根目录下运行此脚本"
        exit 1
    fi
}

# 检测Web服务器
detect_web_server() {
    log_info "检测Web服务器..."
    
    WEB_SERVER=""
    
    # 检查Apache
    if command -v httpd >/dev/null 2>&1 || command -v apache2 >/dev/null 2>&1; then
        if systemctl is-active --quiet httpd 2>/dev/null || systemctl is-active --quiet apache2 2>/dev/null; then
            WEB_SERVER="apache"
            log_success "检测到Apache服务器正在运行"
        else
            log_warning "检测到Apache服务器但未运行"
        fi
    fi
    
    # 检查Nginx
    if command -v nginx >/dev/null 2>&1; then
        if systemctl is-active --quiet nginx 2>/dev/null; then
            if [ -z "$WEB_SERVER" ]; then
                WEB_SERVER="nginx"
                log_success "检测到Nginx服务器正在运行"
            else
                log_info "同时检测到Nginx服务器"
            fi
        else
            log_warning "检测到Nginx服务器但未运行"
        fi
    fi
    
    # 检查PHP内置服务器
    if command -v php >/dev/null 2>&1; then
        log_info "PHP内置服务器可用"
        if [ -z "$WEB_SERVER" ]; then
            WEB_SERVER="php"
        fi
    fi
    
    if [ -z "$WEB_SERVER" ]; then
        log_error "未检测到可用的Web服务器"
        exit 1
    fi
}

# 选择部署方案
choose_deployment_method() {
    echo ""
    log_info "请选择部署方案："
    echo ""
    echo "1. PHP内置服务器 (推荐用于开发/测试)"
    echo "   - 快速启动，无需配置"
    echo "   - 适合开发和测试环境"
    echo ""
    echo "2. Apache虚拟主机"
    echo "   - 生产环境推荐"
    echo "   - 需要配置虚拟主机"
    echo ""
    echo "3. Nginx虚拟主机"
    echo "   - 高性能Web服务器"
    echo "   - 需要配置虚拟主机"
    echo ""
    echo "4. 手动配置指导"
    echo "   - 提供配置文件模板"
    echo "   - 手动完成配置"
    echo ""
    
    read -p "请选择 [1-4]: " choice
    
    case $choice in
        1)
            DEPLOYMENT_METHOD="php"
            log_info "选择了PHP内置服务器"
            ;;
        2)
            DEPLOYMENT_METHOD="apache"
            log_info "选择了Apache虚拟主机"
            ;;
        3)
            DEPLOYMENT_METHOD="nginx"
            log_info "选择了Nginx虚拟主机"
            ;;
        4)
            DEPLOYMENT_METHOD="manual"
            log_info "选择了手动配置指导"
            ;;
        *)
            DEPLOYMENT_METHOD="php"
            log_info "默认选择PHP内置服务器"
            ;;
    esac
}

# PHP内置服务器部署
deploy_php_builtin() {
    log_step "配置PHP内置服务器..."
    
    # 获取端口
    read -p "请输入端口号 [8080]: " port
    port=${port:-8080}
    
    # 获取主机地址
    read -p "请输入绑定地址 [0.0.0.0]: " host
    host=${host:-0.0.0.0}
    
    # 创建启动脚本
    cat > start-server.sh << EOF
#!/bin/bash

# 智能调度系统PHP内置服务器启动脚本

echo "启动智能调度系统..."
echo "访问地址: http://$host:$port"
echo "文档根目录: $(pwd)/public"
echo ""
echo "按 Ctrl+C 停止服务器"
echo ""

cd public
php -S $host:$port
EOF
    
    chmod +x start-server.sh
    
    log_success "PHP内置服务器配置完成"
    echo ""
    log_info "启动命令: ./start-server.sh"
    log_info "访问地址: http://$host:$port"
    
    # 询问是否立即启动
    read -p "是否立即启动服务器？(y/n): " start_now
    if [[ $start_now =~ ^[Yy]$ ]]; then
        log_info "正在启动服务器..."
        ./start-server.sh
    fi
}

# Apache虚拟主机配置
deploy_apache() {
    log_step "配置Apache虚拟主机..."
    
    # 获取域名
    read -p "请输入域名 [smart-dispatch.local]: " domain
    domain=${domain:-smart-dispatch.local}
    
    # 获取端口
    read -p "请输入端口 [80]: " port
    port=${port:-80}
    
    # 获取项目路径
    PROJECT_PATH=$(pwd)
    
    # 创建虚拟主机配置
    VHOST_CONFIG="<VirtualHost *:$port>
    ServerName $domain
    DocumentRoot $PROJECT_PATH/public
    
    <Directory $PROJECT_PATH/public>
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        
        # URL重写规则
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php/\$1 [QSA,PT,L]
    </Directory>
    
    # 日志配置
    ErrorLog /var/log/httpd/smart-dispatch-error.log
    CustomLog /var/log/httpd/smart-dispatch-access.log combined
</VirtualHost>"
    
    # 保存配置文件
    echo "$VHOST_CONFIG" > smart-dispatch.conf
    
    log_success "Apache虚拟主机配置文件已生成: smart-dispatch.conf"
    echo ""
    log_info "手动配置步骤："
    echo "1. 复制配置文件到Apache配置目录："
    echo "   sudo cp smart-dispatch.conf /etc/httpd/conf.d/"
    echo "   # 或者"
    echo "   sudo cp smart-dispatch.conf /etc/apache2/sites-available/"
    echo "   sudo a2ensite smart-dispatch"
    echo ""
    echo "2. 添加域名解析到 /etc/hosts："
    echo "   echo '127.0.0.1 $domain' | sudo tee -a /etc/hosts"
    echo ""
    echo "3. 重启Apache服务："
    echo "   sudo systemctl restart httpd"
    echo "   # 或者"
    echo "   sudo systemctl restart apache2"
    echo ""
    echo "4. 访问: http://$domain"
}

# Nginx虚拟主机配置
deploy_nginx() {
    log_step "配置Nginx虚拟主机..."
    
    # 获取域名
    read -p "请输入域名 [smart-dispatch.local]: " domain
    domain=${domain:-smart-dispatch.local}
    
    # 获取端口
    read -p "请输入端口 [80]: " port
    port=${port:-80}
    
    # 获取项目路径
    PROJECT_PATH=$(pwd)
    
    # 创建Nginx配置
    NGINX_CONFIG="server {
    listen $port;
    server_name $domain;
    root $PROJECT_PATH/public;
    index index.php index.html;
    
    # 日志配置
    access_log /var/log/nginx/smart-dispatch-access.log;
    error_log /var/log/nginx/smart-dispatch-error.log;
    
    # 静态文件处理
    location ~* \.(css|js|png|jpg|jpeg|gif|ico|svg|woff|woff2|ttf|eot)$ {
        expires 1y;
        add_header Cache-Control \"public, immutable\";
    }
    
    # PHP文件处理
    location ~ \.php$ {
        try_files \$uri =404;
        fastcgi_split_path_info ^(.+\.php)(/.+)$;
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }
    
    # URL重写
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    # 安全配置
    location ~ /\. {
        deny all;
    }
    
    location ~ /(application|runtime|vendor) {
        deny all;
    }
}"
    
    # 保存配置文件
    echo "$NGINX_CONFIG" > smart-dispatch-nginx.conf
    
    log_success "Nginx虚拟主机配置文件已生成: smart-dispatch-nginx.conf"
    echo ""
    log_info "手动配置步骤："
    echo "1. 复制配置文件到Nginx配置目录："
    echo "   sudo cp smart-dispatch-nginx.conf /etc/nginx/sites-available/smart-dispatch"
    echo "   sudo ln -s /etc/nginx/sites-available/smart-dispatch /etc/nginx/sites-enabled/"
    echo ""
    echo "2. 添加域名解析到 /etc/hosts："
    echo "   echo '127.0.0.1 $domain' | sudo tee -a /etc/hosts"
    echo ""
    echo "3. 测试Nginx配置："
    echo "   sudo nginx -t"
    echo ""
    echo "4. 重启Nginx服务："
    echo "   sudo systemctl restart nginx"
    echo ""
    echo "5. 确保PHP-FPM运行："
    echo "   sudo systemctl start php-fpm"
    echo ""
    echo "6. 访问: http://$domain"
}

# 手动配置指导
manual_configuration() {
    log_step "生成手动配置指导..."
    
    PROJECT_PATH=$(pwd)
    
    cat > deployment-guide.md << EOF
# 智能调度系统部署指南

## 项目信息
- 项目路径: $PROJECT_PATH
- 文档根目录: $PROJECT_PATH/public
- 入口文件: public/index.php

## 环境要求
- PHP 7.4+
- MySQL 5.7+
- Web服务器 (Apache/Nginx)

## Apache配置示例

### 虚拟主机配置
\`\`\`apache
<VirtualHost *:80>
    ServerName your-domain.com
    DocumentRoot $PROJECT_PATH/public
    
    <Directory $PROJECT_PATH/public>
        AllowOverride All
        Require all granted
        DirectoryIndex index.php
        
        RewriteEngine On
        RewriteCond %{REQUEST_FILENAME} !-f
        RewriteCond %{REQUEST_FILENAME} !-d
        RewriteRule ^(.*)$ index.php/\$1 [QSA,PT,L]
    </Directory>
</VirtualHost>
\`\`\`

### .htaccess文件
\`\`\`apache
RewriteEngine On
RewriteCond %{REQUEST_FILENAME} !-f
RewriteCond %{REQUEST_FILENAME} !-d
RewriteRule ^(.*)$ index.php/\$1 [QSA,PT,L]
\`\`\`

## Nginx配置示例

\`\`\`nginx
server {
    listen 80;
    server_name your-domain.com;
    root $PROJECT_PATH/public;
    index index.php;
    
    location / {
        try_files \$uri \$uri/ /index.php?\$query_string;
    }
    
    location ~ \.php$ {
        fastcgi_pass 127.0.0.1:9000;
        fastcgi_index index.php;
        fastcgi_param SCRIPT_FILENAME \$document_root\$fastcgi_script_name;
        include fastcgi_params;
    }
}
\`\`\`

## 目录权限设置

\`\`\`bash
# 设置运行时目录权限
chmod -R 755 runtime
chown -R www-data:www-data runtime

# 设置上传目录权限
chmod -R 755 public/uploads
chown -R www-data:www-data public/uploads
\`\`\`

## 数据库配置

编辑 \`application/database.php\` 文件，配置数据库连接信息。

## 访问系统

配置完成后，访问 http://your-domain.com 进行系统初始化。

## 故障排除

1. 检查PHP版本和扩展
2. 检查目录权限
3. 检查Web服务器配置
4. 检查数据库连接
5. 查看错误日志
EOF
    
    log_success "部署指南已生成: deployment-guide.md"
}

# 检查系统状态
check_system_status() {
    log_step "检查系统状态..."
    
    echo ""
    log_info "系统环境检查："
    
    # PHP版本
    if command -v php >/dev/null 2>&1; then
        PHP_VERSION=$(php -r "echo PHP_VERSION;")
        log_success "PHP版本: $PHP_VERSION"
    else
        log_error "PHP未安装"
    fi
    
    # 检查关键目录
    if [ -d "runtime" ]; then
        RUNTIME_PERM=$(stat -c "%a" runtime)
        log_success "runtime目录权限: $RUNTIME_PERM"
    else
        log_warning "runtime目录不存在"
    fi
    
    if [ -d "public/uploads" ]; then
        UPLOADS_PERM=$(stat -c "%a" public/uploads)
        log_success "uploads目录权限: $UPLOADS_PERM"
    else
        log_warning "uploads目录不存在"
    fi
    
    # 检查关键文件
    if [ -f "public/index.php" ]; then
        log_success "入口文件存在"
    else
        log_error "入口文件缺失"
    fi
    
    if [ -f "application/database.php" ]; then
        log_success "数据库配置文件存在"
    else
        log_warning "数据库配置文件不存在"
    fi
}

# 生成部署报告
generate_deployment_report() {
    log_step "生成部署报告..."
    
    cat > deployment-report.txt << EOF
智能调度系统Web服务器部署报告
生成时间: $(date)

=== 部署信息 ===
部署方法: $DEPLOYMENT_METHOD
项目路径: $(pwd)
文档根目录: $(pwd)/public

=== 系统状态 ===
PHP版本: $(php -r "echo PHP_VERSION;" 2>/dev/null || echo "未安装")
Web服务器: $WEB_SERVER
运行用户: $(whoami)

=== 目录权限 ===
runtime: $(stat -c "%a" runtime 2>/dev/null || echo "不存在")
uploads: $(stat -c "%a" public/uploads 2>/dev/null || echo "不存在")

=== 配置文件 ===
数据库配置: $([ -f "application/database.php" ] && echo "存在" || echo "不存在")
入口文件: $([ -f "public/index.php" ] && echo "存在" || echo "不存在")

=== 下一步操作 ===
1. 确保数据库配置正确
2. 启动Web服务器
3. 访问系统进行初始化
4. 查看错误日志排除问题

=== 访问信息 ===
$(case $DEPLOYMENT_METHOD in
    "php") echo "启动命令: ./start-server.sh" ;;
    "apache") echo "配置文件: smart-dispatch.conf" ;;
    "nginx") echo "配置文件: smart-dispatch-nginx.conf" ;;
    "manual") echo "参考文档: deployment-guide.md" ;;
esac)
EOF
    
    log_success "部署报告已生成: deployment-report.txt"
}

# 主函数
main() {
    check_project_root
    detect_web_server
    choose_deployment_method
    
    case $DEPLOYMENT_METHOD in
        "php")
            deploy_php_builtin
            ;;
        "apache")
            deploy_apache
            ;;
        "nginx")
            deploy_nginx
            ;;
        "manual")
            manual_configuration
            ;;
    esac
    
    check_system_status
    generate_deployment_report
    
    echo ""
    echo "========================================"
    log_success "Web服务器配置完成！"
    echo "========================================"
    echo ""
    log_info "下一步操作："
    echo "1. 确保数据库配置正确"
    echo "2. 启动Web服务器"
    echo "3. 访问系统进行初始化"
    echo ""
    log_info "相关文件："
    echo "- 部署报告: deployment-report.txt"
    if [ "$DEPLOYMENT_METHOD" = "manual" ]; then
        echo "- 部署指南: deployment-guide.md"
    fi
}

# 运行主函数
main "$@"
